import request from '@/utils/request'
import type {
  CustomField,
  CustomFieldCreate,
  CustomFieldUpdate,
  CustomFieldListResponse,
  AssetCustomFieldValue,
  AssetCustomFieldValueCreate,
  AssetCustomFieldValueUpdate,
  InventoryRecordCustomFieldValue,
  InventoryRecordCustomFieldValueCreate,
  InventoryRecordCustomFieldValueUpdate,
  BatchSetCustomFieldValuesRequest,
  FieldPresetRequest,
  FileUploadResponse,
  FieldSortItem
} from '@/types/custom_field'

export const customFieldApi = {
  // ============ 自定义字段管理 API ============
  
  // 获取自定义字段列表
  getCustomFields: (params: {
    skip?: number
    limit?: number
    keyword?: string
    field_type?: string
    applies_to?: string
    is_active?: boolean
  }) => {
    return request.get<CustomFieldListResponse>('/custom-fields/', { params })
  },

  // 获取启用的自定义字段列表
  getActiveCustomFields: (params?: { applies_to?: string }) => {
    return request.get<CustomField[]>('/custom-fields/active', { params })
  },

  // 创建自定义字段
  createCustomField: (data: CustomFieldCreate) => {
    return request.post<CustomField>('/custom-fields/', data)
  },

  // 获取指定自定义字段
  getCustomField: (id: number) => {
    return request.get<CustomField>(`/custom-fields/${id}`)
  },

  // 更新自定义字段
  updateCustomField: (id: number, data: CustomFieldUpdate) => {
    return request.put<CustomField>(`/custom-fields/${id}`, data)
  },

  // 删除自定义字段
  deleteCustomField: (id: number) => {
    return request.delete(`/custom-fields/${id}`)
  },

  // 批量更新字段排序
  batchUpdateSortOrders: (fieldOrders: FieldSortItem[]) => {
    return request.post('/custom-fields/batch-sort', fieldOrders)
  },

  // 创建预设字段配置
  createPresetFields: (data: FieldPresetRequest) => {
    return request.post<CustomField[]>('/custom-fields/presets', data)
  },

  // ============ 资产自定义字段值 API ============

  // 获取资产的自定义字段值
  getAssetCustomFieldValues: (assetId: number) => {
    return request.get<AssetCustomFieldValue[]>(`/custom-fields/values/asset/${assetId}`)
  },

  // 批量设置资产自定义字段值
  batchSetAssetCustomFieldValues: (assetId: number, data: BatchSetCustomFieldValuesRequest) => {
    return request.post(`/custom-fields/values/asset/${assetId}/batch`, data)
  },

  // 更新资产自定义字段值
  updateAssetCustomFieldValue: (valueId: number, data: AssetCustomFieldValueUpdate) => {
    return request.put<AssetCustomFieldValue>(`/custom-fields/values/asset/${valueId}`, data)
  },

  // 删除资产自定义字段值
  deleteAssetCustomFieldValue: (valueId: number) => {
    return request.delete(`/custom-fields/values/asset/${valueId}`)
  },

  // ============ 盘点记录自定义字段值 API ============

  // 获取盘点记录的自定义字段值
  getInventoryRecordCustomFieldValues: (inventoryRecordId: number) => {
    return request.get<InventoryRecordCustomFieldValue[]>(`/custom-fields/values/inventory/${inventoryRecordId}`)
  },

  // 获取虚拟盘点记录的自定义字段值
  getInventoryVirtualRecordCustomFieldValues: (taskId: number, assetId: number) => {
    return request.get<InventoryRecordCustomFieldValue[]>(`/custom-fields/values/inventory/task/${taskId}/asset/${assetId}`)
  },

  // 批量设置盘点记录自定义字段值
  batchSetInventoryRecordCustomFieldValues: (inventoryRecordId: number, data: BatchSetCustomFieldValuesRequest) => {
    return request.post(`/custom-fields/values/inventory/${inventoryRecordId}/batch`, data)
  },

  // 批量设置虚拟盘点记录自定义字段值
  batchSetInventoryVirtualRecordCustomFieldValues: (taskId: number, assetId: number, data: BatchSetCustomFieldValuesRequest) => {
    return request.post(`/custom-fields/values/inventory/task/${taskId}/asset/${assetId}/batch`, data)
  },

  // ============ 文件上传 API ============

  // 上传文件
  uploadFile: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<FileUploadResponse>('/custom-fields/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 自定义字段工具函数
export const customFieldUtils = {
  // 根据字段类型获取默认选项
  getDefaultOptionsForType: (fieldType: string) => {
    switch (fieldType) {
      case 'select':
      case 'radio':
      case 'checkbox':
        return {
          choices: [
            { label: '选项1', value: 'option1' },
            { label: '选项2', value: 'option2' }
          ]
        }
      case 'file':
        return {
          accept: 'image/*',
          max_size: 5242880 // 5MB
        }
      case 'textarea':
        return {
          rows: 4,
          placeholder: '请输入内容'
        }
      case 'number':
        return {
          min: 0,
          step: 1
        }
      default:
        return {}
    }
  },

  // 根据字段类型获取默认验证规则
  getDefaultValidationForType: (fieldType: string) => {
    switch (fieldType) {
      case 'text':
        return {
          max_length: 255
        }
      case 'textarea':
        return {
          max_length: 1000
        }
      case 'number':
        return {
          min_value: 0
        }
      default:
        return {}
    }
  },

  // 验证字段选项配置
  validateFieldOptions: (fieldType: string, options: any) => {
    const errors: string[] = []
    
    if (['select', 'radio', 'checkbox'].includes(fieldType)) {
      if (!options?.choices || !Array.isArray(options.choices) || options.choices.length === 0) {
        errors.push('选择类型字段必须配置选项')
      } else {
        const duplicateValues = options.choices
          .map((choice: any) => choice.value)
          .filter((value: string, index: number, arr: string[]) => arr.indexOf(value) !== index)
        
        if (duplicateValues.length > 0) {
          errors.push('选项值不能重复')
        }
      }
    }
    
    if (fieldType === 'file') {
      if (options?.max_size && options.max_size > 10485760) { // 10MB
        errors.push('文件大小限制不能超过10MB')
      }
    }
    
    return errors
  },

  // 格式化字段值用于显示
  formatFieldValueForDisplay: (field: CustomField, value: string | undefined) => {
    if (!value) return '-'
    
    switch (field.field_type) {
      case 'select':
      case 'radio':
        const choice = field.options?.choices?.find(c => c.value === value)
        return choice?.label || value
      
      case 'checkbox':
        if (field.options?.choices) {
          const values = value.split(',')
          const labels = values.map(v => {
            const choice = field.options?.choices?.find(c => c.value === v.trim())
            return choice?.label || v.trim()
          })
          return labels.join(', ')
        }
        return value
      
      case 'file':
        // 如果是文件URL，显示文件名
        if (value.startsWith('/uploads/') || value.startsWith('http')) {
          const filename = value.split('/').pop() || value
          return filename
        }
        return value
      
      case 'date':
        try {
          return new Date(value).toLocaleDateString('zh-CN')
        } catch {
          return value
        }
      
      case 'datetime':
        try {
          return new Date(value).toLocaleString('zh-CN')
        } catch {
          return value
        }
      
      default:
        return value
    }
  },

  // 获取文件预览URL
  getFilePreviewUrl: (fileUrl: string) => {
    if (fileUrl.startsWith('http')) {
      return fileUrl
    }
    // 导入API_BASE_URL并去掉'/api/v1'后缀
    const { API_BASE_URL } = require('@/config/api')
    const baseUrl = API_BASE_URL.replace('/api/v1', '')
    return baseUrl + fileUrl
  }
} 